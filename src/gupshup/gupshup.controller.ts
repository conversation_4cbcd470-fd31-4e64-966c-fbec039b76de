import { Body, Controller, Post } from '@nestjs/common';
import {
  Ctx,
  MessagePattern,
  Payload,
  RmqContext,
} from '@nestjs/microservices';
import { GupshupService } from './gupshup.service';
@Controller('gupshup')
export class GupshupController {
  constructor(private readonly gupshupService: GupshupService) {}

  @Post('webhook')
  async webhook(@Body() data: any) {
    try {
      console.log('Glória a Deus!!!\\o/==>', data);
      // if (data) {}
      return this.gupshupService.processEvent(data);
    } catch (err: any) {
      console.log('erro no webhook', err);
      console.log(err);
    }
  }
  @MessagePattern('new-message')
  async newRabbitMQEvent(@Payload() data: any, @Ctx() context: RmqContext) {
    const channel = context.getChannelRef();
    const originalMsg = context.getMessage();

    try {
      await this.gupshupService.processEvent(data);
    } catch (err: any) {
      console.log(err);
    } finally {
      channel.ack(originalMsg);
    }
  }

  // @Post()
  // async createGupshupEvent(@Body() data: any) {
  //   try {
  //     await this.gupshupService.processEvent(data);
  //   } catch (err: any) {
  //     console.log(err);
  //   }
  // }
}
