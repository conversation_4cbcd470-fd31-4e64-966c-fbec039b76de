import { Message } from '@aws-sdk/client-sqs';
import { Injectable } from '@nestjs/common';
import { SqsConsumerEventHandler, SqsMessageHandler } from '@ssut/nestjs-sqs';
import { GupshupService } from './gupshup.service';
import { SqsQueueEnum } from 'src/shared/types/SqsQueueEnum';

@Injectable()
export class GupshupWebhookHandlerService {
  constructor(private readonly gupshupService: GupshupService) {}

  @SqsMessageHandler(SqsQueueEnum.GUPSHUP_EVENTS, false)
  public async handleMessage(message: Message) {
    console.log('Glória a Deus!!!\\o/==>handleMessage', message);
    try {
      const data = JSON.parse(message.Body || '{}');
      this.gupshupService.processEvent(data);
    } catch (err: any) {
      console.log(err);
    }
  }

  @SqsConsumerEventHandler(SqsQueueEnum.GUPSHUP_EVENTS, 'processing_error')
  public onProcessingError(error: Error, message: Message) {
    console.log('Glória a Deus!!!\\o/==>onProcessingError');
    // report errors here
    console.log({ error, message });
  }
}
