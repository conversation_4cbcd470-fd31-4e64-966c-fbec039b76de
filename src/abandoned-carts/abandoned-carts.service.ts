import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
} from '@nestjs/common';
import { AbandonedCart, MessageTemplateType, Prisma } from '@prisma/client';
import { subDays } from 'date-fns';
import { ConversationsService } from 'src/conversations/conversations.service';
import { FlowEventsService } from 'src/flow-events/flow-events.service';
import { FlowTriggersService } from 'src/flow-triggers/flow-triggers.service';
import { FlowsService } from 'src/flows/flows.service';
import { MessagesService } from 'src/messages/messages.service';
import { TemplateParametersEnum } from 'src/shared/types/TemplateParametersEnum';
import { PhoneNumberUtils } from 'src/shared/utils/phone-number.utils';
import { PrismaService } from './../prisma/prisma.service';
import { SaveAndSendAbandonedCartDto } from './dto/save-and-send-abandoned-cart.dto';
import { EmailsService } from 'src/emails/emails.service';
import { EmailTemplateParametersEnum } from 'src/shared/types/EmailTemplateParametersEnum';
@Injectable()
export class AbandonedCartsService {
  constructor(
    private readonly prismaService: PrismaService,
    @Inject(forwardRef(() => MessagesService))
    private readonly messagesService: MessagesService,
    private readonly flowsService: FlowsService,
    private readonly flowEventsService: FlowEventsService,
    @Inject(forwardRef(() => ConversationsService))
    private readonly conversationsService: ConversationsService,
    private readonly flowTriggersService: FlowTriggersService,
    @Inject(forwardRef(() => EmailsService))
    private readonly emailsService: EmailsService,
  ) {}

  async bulkCreateAbandonedCarts(
    data: Prisma.AbandonedCartUncheckedCreateInput[],
  ) {
    return await this.prismaService.abandonedCart.createMany({
      data,
      skipDuplicates: true,
    });
  }

  async listAbandonedCarts(params: {
    skip?: number;
    take?: number;
    cursor?: Prisma.AbandonedCartWhereUniqueInput;
    where?: Prisma.AbandonedCartWhereInput;
    orderBy?: Prisma.AbandonedCartOrderByWithRelationInput;
  }) {
    const { skip, take, cursor, where, orderBy } = params;
    return await this.prismaService.abandonedCart.findMany({
      skip,
      take,
      cursor,
      where,
      orderBy,
    });
  }

  async createAbandonedCart(data: Prisma.AbandonedCartUncheckedCreateInput) {
    const formattedPhoneNumberId = this.getFormattedPhoneNumber(
      data.customerPhoneNumber,
    );
    return await this.prismaService.abandonedCart.create({
      data: {
        ...data,
        phoneNumberId: formattedPhoneNumberId,
      },
    });
  }

  async updateAbandonedCart(params: {
    where: Prisma.AbandonedCartWhereUniqueInput;
    data: Prisma.AbandonedCartUncheckedUpdateInput;
  }) {
    const { where, data } = params;
    return await this.prismaService.abandonedCart.update({
      data,
      where,
    });
  }

  async saveAndSendAbandonedCart(
    saveAndSendAbandonedCartDto: SaveAndSendAbandonedCartDto,
  ) {
    const formattedPhoneNumberId = this.getFormattedPhoneNumber(
      saveAndSendAbandonedCartDto.customerPhoneNumber,
    );

    const abandonedCart = await this.createAbandonedCart({
      ...saveAndSendAbandonedCartDto,
      sourceCreatedAt: new Date(saveAndSendAbandonedCartDto.sourceCreatedAt),
      phoneNumberId: formattedPhoneNumberId,
    });

    await this.validateAbandonedCart(abandonedCart!);
    await this.sendAbandonedCartMessage(abandonedCart!);

    return abandonedCart;
  }

  private async sendAbandonedCartMessage(abandonedCart: AbandonedCart) {
    const company = await this.prismaService.company.findUnique({
      where: { id: abandonedCart.companyId },
    });

    const automation = await this.getActiveAbandonedCartAutomation(
      abandonedCart.companyId,
    );

    const conversation =
      await this.conversationsService.findOrCreateConversation(
        {
          companyId: company!.id,
          recipientPhoneNumberId: abandonedCart.phoneNumberId!,
          recipientName: abandonedCart.customerName!,
        },
        {
          shouldCreateConversationTicket: false,
          shouldSkipEmitNewConversationEvent: false,
        },
      );

    if (automation.action === 'send_message_template') {
      if (abandonedCart.status === 'whatsapp_and_email_sent') {
        return true;
      }

      let isWhatsappSent = abandonedCart.status === 'whatsapp_sent';
      let isEmailSent = abandonedCart.status === 'email_sent';

      if (
        automation.messageTemplateId &&
        automation.messageTemplate &&
        !isWhatsappSent
      ) {
        try {
          await this.messagesService.sendMessageTemplateByPhone({
            companyId: abandonedCart.companyId,
            recipientName: abandonedCart.customerName!,
            recipientPhoneNumberId: abandonedCart.phoneNumberId!,
            senderPhoneNumberId: company!.phoneNumberId,
            shouldCreateConversationTicket: false,
            templateName: automation.messageTemplate.name,
            templateArgs: {
              [TemplateParametersEnum.CTA_LINK as string]:
                abandonedCart.cartUrl,
              [TemplateParametersEnum.ABANDONED_CART_PRODUCTS as string]:
                Array.isArray(abandonedCart.products)
                  ? abandonedCart.products.map((p: any) => p.name).join(', ')
                  : '.',
            },
            automationId: automation.id,
          });
          isWhatsappSent = true;
        } catch (err: any) {}
      }

      if (
        automation.emailTemplateId &&
        automation.emailTemplate &&
        !isEmailSent
      ) {
        try {
          await this.emailsService.sendEmailTemplateTest(
            {
              emailTemplateId: automation.emailTemplate.id,
              recipientName: abandonedCart.customerName!,
              recipientEmail: abandonedCart.customerEmail!,
              templateArgs: {
                [EmailTemplateParametersEnum.CTA_LINK]: abandonedCart.cartUrl!,
                [EmailTemplateParametersEnum.ABANDONED_CART_PRODUCTS]:
                  Array.isArray(abandonedCart.products)
                    ? abandonedCart.products.map((p: any) => p.name).join(', ')
                    : '.',
              },
            },
            company!.id,
          );
          isEmailSent = true;
        } catch (err: any) {}
      }

      const status =
        isWhatsappSent && isEmailSent
          ? 'whatsapp_and_email_sent'
          : isWhatsappSent
          ? 'whatsapp_sent'
          : isEmailSent
          ? 'email_sent'
          : undefined;

      if (!status) {
        return false;
      }

      await this.updateAbandonedCart({
        where: { id: abandonedCart.id },
        data: {
          status,
        },
      });

      return true;
    }

    if (automation.action === 'trigger_flow') {
      const flowTrigger = await this.flowTriggersService.findFlowTrigger({
        flowId: automation.flowId,
        type: 'abandoned_cart',
      });
      await this.flowEventsService.createFlowEvent({
        companyId: company!.id,
        conversationId: conversation.id,
        flowId: automation.flowId,
        flowTriggerId: flowTrigger!.id,
        metadata: {
          automationId: automation.id,
        },
        fromSystem: true,
      });
      await this.flowsService.triggerAbandonedCartFlow(
        automation.flowId,
        abandonedCart.phoneNumberId!,
        abandonedCart.customerName!,
      );

      await this.updateAbandonedCart({
        where: { id: abandonedCart.id },
        data: {
          status: 'flow_triggered',
        },
      });

      return true;
    }
  }

  private async validateAbandonedCart(abandonedCart: AbandonedCart) {
    if (!abandonedCart.phoneNumberId) {
      const errorMessage = 'Telefone inválido';
      await this.updateAbandonedCart({
        where: { id: abandonedCart.id },
        data: {
          errorMessage,
          status: 'failed',
        },
      });
      throw new BadRequestException(errorMessage);
    }

    if (!abandonedCart.customerName) {
      const errorMessage = 'Nome do cliente não informado';
      await this.updateAbandonedCart({
        where: { id: abandonedCart.id },
        data: {
          errorMessage,
          status: 'failed',
        },
      });
      throw new BadRequestException(errorMessage);
    }

    const canSendCartRecoveryMessage = await this.canSendCartRecoveryMessage({
      companyId: abandonedCart.companyId,
      phoneNumberId: abandonedCart.phoneNumberId,
    });

    if (!canSendCartRecoveryMessage) {
      const errorMessage =
        'Cliente já possui um carrinho abandonado ou pedido recente';
      await this.updateAbandonedCart({
        where: { id: abandonedCart.id },
        data: {
          errorMessage,
          status: 'failed',
        },
      });
      throw new BadRequestException(errorMessage);
    }

    return true;
  }

  private async canSendCartRecoveryMessage(params: {
    companyId: string;
    phoneNumberId: string;
  }) {
    const { companyId, phoneNumberId } = params;
    const MIN_DAYS_WITHOUT_ORDER = 3;
    const MIN_DAYS_WITHOUT_ABANDONED_CART = 7;
    const excludedPhoneNumbers: { phone_number_id: string }[] = await this
      .prismaService.$queryRaw`
      select
        distinct (c.phone_number_id)
      from
        customers c
      where
        c.phone_number_id = ${phoneNumberId}
        and (
          exists (
            select 1 from messages m
            join message_templates mt on mt.id = m.message_template_id
            join conversations conv on conv.id = m.conversation_id
            where conv.customer_id = c.id
            and m.created_at > ${subDays(
              new Date(),
              MIN_DAYS_WITHOUT_ABANDONED_CART,
            )}
            and mt.type::text = ${MessageTemplateType.ABANDONED_CART}
            and conv.company_id = ${companyId}
          ) or exists (
            select 1 from orders o
            where o.customer_id = c.id
            and o.source_created_at > ${subDays(
              new Date(),
              MIN_DAYS_WITHOUT_ORDER,
            )}
            and o.company_id = ${companyId}
          ) or exists (
          select 1 from abandoned_carts ac
            where ac.company_id = ${companyId}
            and ac.customer_phone_number_id = ${phoneNumberId}
            and ac.status in ('whatsapp_sent', 'flow_triggered')
            and ac.source_created_at >= ${subDays(
              new Date(),
              MIN_DAYS_WITHOUT_ABANDONED_CART,
            )}
          )
        );
    `;

    return excludedPhoneNumbers.length === 0;
  }

  private getFormattedPhoneNumber(
    phoneNumber: string | null | undefined,
  ): string | null {
    if (!phoneNumber) {
      return null;
    }

    const formattedPhoneNumber =
      PhoneNumberUtils.formatPhoneNumber(phoneNumber);
    return PhoneNumberUtils.isValidPhoneNumber(formattedPhoneNumber)
      ? formattedPhoneNumber
      : null;
  }

  private async getActiveAbandonedCartAutomation(
    companyId: string,
  ): Promise<any> {
    const automation = await this.prismaService.automation.findFirst({
      where: {
        companyId,
        automationType: { slug: 'abandoned_cart' },
        isActive: true,
      },
      include: { messageTemplate: true, emailTemplate: true, flow: true },
    });

    if (!automation) {
      throw new BadRequestException(
        `Automação de carrinho abandonado não encontrada para a empresa ${companyId}`,
      );
    }

    return automation;
  }

  async sendScheduledAbandonedCartMessages(companyId: string) {
    const abandonedCarts = await this.listAbandonedCarts({
      where: {
        companyId,
        status: 'abandoned',
        sourceCreatedAt: {
          gte: subDays(new Date(), 1),
        },
        scheduledSendTime: {
          lte: new Date(),
        },
      },
    });

    for (const abandonedCart of abandonedCarts) {
      try {
        await this.validateAbandonedCart(abandonedCart);
        await this.sendAbandonedCartMessage(abandonedCart);
      } catch (err: any) {
        await this.updateAbandonedCart({
          where: { id: abandonedCart.id },
          data: {
            errorMessage: err.message,
            status: 'failed',
          },
        });
      }
    }

    return true;
  }

  async countAbandonedCarts(params: { where: any }): Promise<number> {
    return this.prismaService.abandonedCart.count({
      where: params.where,
    });
  }
}
