import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { MediaType } from '@prisma/client';
import axios from 'axios';
import { CompanyWithIncludes } from 'src/companies/types/CompanyWithIncludes';
import { ConversationTicketsService } from 'src/conversation-tickets/conversation-tickets.service';
import { ConversationsService } from 'src/conversations/conversations.service';
import { ConversationWithIncludes } from 'src/conversations/types/ConversationWithIncludes';
import { FilesService } from 'src/files/files.service';
import { SendProductCatalogDto } from 'src/messages/dto/send-product-catalog.dto';
import { MessagesService } from 'src/messages/messages.service';

@Injectable()
export class AiAgentsService {
  constructor(
    private readonly filesService: FilesService,
    @Inject(forwardRef(() => MessagesService))
    private readonly messagesService: MessagesService,
    @Inject(forwardRef(() => ConversationTicketsService))
    private readonly conversationTicketService: ConversationTicketsService,
    private readonly configService: ConfigService,
  ) {}

  async processCustomerMessage({
    company,
    conversation,
  }: {
    company: CompanyWithIncludes;
    conversation: ConversationWithIncludes;
    qtyMessagesSent: number;
  }) {
    try {
      const { data: aiAgentResponse } = await axios.post(
        `${this.configService.get('REVI_N8N_URL')}/webhook/agent-message`,
        {
          customerId: conversation.customerId,
          companyId: company.id,
          conversationId: conversation.id,
          qtyMessagesSent: conversation.messages.length,
        },
        {
          headers: {
            accept: 'application/json',
            'Content-Type': 'application/json',
          },
        },
      );
      const response: {
        image_url: string[];
        messages: string[];
        catalog?: SendProductCatalogDto;
        transfer_to_human_agent: boolean;
      } =
        typeof aiAgentResponse !== 'string'
          ? aiAgentResponse
          : JSON.parse(aiAgentResponse);

      if (response.image_url.length === 1) {
        const file = await this.filesService.downloadFileFromUrl(
          response.image_url[0],
        );
        const uploadedFile = await this.filesService.uploadFile({
          companyId: company.id,
          file,
          keyPrefix: 'ai-agent-replies/media',
        });
        if (uploadedFile) {
          await this.messagesService.sendMessage({
            isAutomaticResponse: true,
            companyId: company.id,
            senderPhoneNumberId: company.phoneNumberId,
            conversationId: conversation.id,
            mediaType: uploadedFile.mediaType || MediaType.image,
            fileKey: uploadedFile.key,
            text: response.messages[0],
          });
        }
      }

      for (const message of response.messages) {
        await this.messagesService.sendMessage({
          isAutomaticResponse: true,
          companyId: company.id,
          senderPhoneNumberId: company.phoneNumberId,
          conversationId: conversation.id,
          text: message,
        });
      }
      if (response.catalog) {
        response.catalog.sections = response.catalog.sections?.map(
          (section) => ({
            ...section,
            title: 'Destaques',
          }),
        );
        response.catalog.footerText = 'Veja o que separei pra você';
        await this.messagesService.sendProductCatalog(response.catalog);
      }
      if (response.transfer_to_human_agent) {
        const ticket =
          await this.conversationTicketService.findOrCreateConversationTicketByConversation(
            conversation.id,
          );
        await this.conversationTicketService.updateConversationTicket({
          where: {
            id: ticket.id,
          },
          data: {
            agentId: null,
          },
          notifyUser: false,
          companyId: company.id,
        });
      }
      return true;
    } catch (error: any) {
      if (conversation.id) {
        await this.messagesService.sendMessage({
          isAutomaticResponse: true,
          companyId: company.id,
          senderPhoneNumberId: company.phoneNumberId,
          conversationId: conversation.id,
          text: 'Eita, tive um problema, poderia repetir?',
        });
        return true;
      }
      return false;
    }
  }
}
