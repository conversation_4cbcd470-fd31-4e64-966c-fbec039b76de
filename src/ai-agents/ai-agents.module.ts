import { forwardRef, Module } from '@nestjs/common';
import { AiAgentsService } from './ai-agents.service';
import { FilesModule } from 'src/files/files.module';
import { MessagesModule } from 'src/messages/messages.module';
import { ConfigModule } from '@nestjs/config';
import { ConversationTicketsModule } from 'src/conversation-tickets/conversation-tickets.module';

@Module({
  exports: [AiAgentsService],
  providers: [AiAgentsService],
  imports: [
    FilesModule,
    forwardRef(() => MessagesModule),
    forwardRef(() => ConversationTicketsModule),
    ConfigModule,
  ],
})
export class AiAgentsModule {}
