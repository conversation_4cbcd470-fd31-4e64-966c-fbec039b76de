import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { ProductsService } from 'src/products/products.service';
import { LogSource, SourceIntegration } from '@prisma/client';
import { CartsService } from 'src/carts/services/carts.service';
import {
  EventType,
  GupshupEvent,
  MessagePayloadType,
} from 'src/gupshup/dto/webhook-event.dto';
import { PrismaService } from 'src/prisma/prisma.service';
import { CartItemDTO } from 'src/carts/dtos/add_cart_item_params.dto';
import { LogsService } from 'src/logs/logs.service';

@Injectable()
export class ProductCatalogService {
  constructor(
    private readonly productsService: ProductsService,
    private readonly cartsService: CartsService,
    private readonly prismaService: PrismaService,
    private readonly logsService: LogsService,
  ) {}

  async getCatalogById(companyId: string, catalogId: string) {
    const catalog = await this.prismaService.productCatalog.findFirst({
      where: {
        id: catalogId,
        companyId,
      },
    });

    if (!catalog) {
      throw new NotFoundException(
        `Catálogo ${catalogId} não encontrado para empresa ${companyId}`,
      );
    }

    return catalog;
  }

  async createCatalog(data: {
    companyId: string;
    externalCatalogId: string;
    name: string;
    isActive?: boolean;
  }) {
    const existing = await this.prismaService.productCatalog.findFirst({
      where: {
        companyId: data.companyId,
        externalCatalogId: data.externalCatalogId,
      },
    });

    if (existing) {
      throw new BadRequestException(
        `Catálogo com id externo "${data.externalCatalogId}" já existe para essa empresa.`,
      );
    }

    const created = await this.prismaService.productCatalog.create({
      data: {
        companyId: data.companyId,
        externalCatalogId: data.externalCatalogId,
        name: data.name,
        isActive: data.isActive ?? false,
      },
    });

    return created;
  }

  async processWhatsAppOrder(params: {
    event: GupshupEvent<EventType.MESSAGE, MessagePayloadType.ORDER>;
    companyId: string;
    customerId: string;
  }) {
    const { event, companyId, customerId } = params;

    const catalogId = event.payload.context?.catalog?.id;

    if (!catalogId) {
      throw new BadRequestException('Catalogo ID não encontrado');
    }

    const items = event.payload.context?.catalog?.order?.items || [];

    if (items.length === 0) {
      throw new Error('Nenhum item encontrado no pedido');
    }

    const externalRetailerIds = items.map((item) => item.id);

    const { mappings, source } =
      await this.productsService.getVariantMappingsWithSource({
        companyId,
        catalogId,
        externalRetailerIds,
      });

    const cartItems: CartItemDTO[] = [];

    for (const item of items) {
      try {
        const mapping = mappings[item.id];

        if (!mapping) {
          throw new Error(`Produto ${item.id} não encontrado no catálogo`);
        }

        cartItems.push({
          productVariantId: mapping.variantId,
          quantity: Number(item.quantity),
          price: Number(item.amount), // Usando o preço do payload
        });
      } catch (error: any) {
        console.warn(`Erro ao mapear item ${item.id}:`, error);
        await this.logsService.createErrorLog(
          {
            type: 'receiveWhatsappCartOrders',
            source: LogSource.internal,
            message: error.message,
            companyId,
            meta: {
              itemId: item.id,
              catalogId,
              customerId,
            },
          },
          error,
        );
      }
    }

    if (cartItems.length === 0) {
      throw new Error('Nenhum item válido encontrado para o carrinho');
    }

    return await this.cartsService.addItemsToCartByCustomerId({
      companyId,
      customerId,
      source,
      items: cartItems,
    });
  }
}
