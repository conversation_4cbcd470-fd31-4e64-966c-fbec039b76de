-- AlterEnum
ALTER TYPE "LogType" ADD VALUE 'productEvents';

-- CreateTable
CREATE TABLE "product_catalogs" (
    "id" TEXT NOT NULL,
    "company_id" TEXT NOT NULL,
    "external_catalog_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "product_catalogs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "catalog_product_mappings" (
    "id" TEXT NOT NULL,
    "catalog_id" TEXT NOT NULL,
    "external_product_id" TEXT NOT NULL,
    "product_variant_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "catalog_product_mappings_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "product_catalogs_company_id_idx" ON "product_catalogs"("company_id");

-- CreateIndex
CREATE INDEX "product_catalogs_external_catalog_id_idx" ON "product_catalogs"("external_catalog_id");

-- CreateIndex
CREATE INDEX "catalog_product_mappings_product_variant_id_idx" ON "catalog_product_mappings"("product_variant_id");

-- CreateIndex
CREATE INDEX "catalog_product_mappings_catalog_id_idx" ON "catalog_product_mappings"("catalog_id");

-- CreateIndex
CREATE UNIQUE INDEX "catalog_product_mappings_catalog_id_external_product_id_key" ON "catalog_product_mappings"("catalog_id", "external_product_id");

-- AddForeignKey
ALTER TABLE "product_catalogs" ADD CONSTRAINT "product_catalogs_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "catalog_product_mappings" ADD CONSTRAINT "catalog_product_mappings_catalog_id_fkey" FOREIGN KEY ("catalog_id") REFERENCES "product_catalogs"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "catalog_product_mappings" ADD CONSTRAINT "catalog_product_mappings_product_variant_id_fkey" FOREIGN KEY ("product_variant_id") REFERENCES "product_variants"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
